import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, CheckCircle, Clock, XCircle } from 'lucide-react';

interface PropertyHeaderProps {
  totalProperties: number;
  activeProperties: number;
  pendingProperties: number;
  blockedProperties: number;
}

const PropertyHeader: React.FC<PropertyHeaderProps> = ({
  totalProperties,
  activeProperties,
  pendingProperties,
  blockedProperties
}) => {
  const stats = [
    {
      title: 'Total Properties',
      value: totalProperties,
      icon: Building,
      color: 'text-blue-600'
    },
    {
      title: 'Active Properties',
      value: activeProperties,
      icon: CheckCircle,
      color: 'text-green-600'
    },
    {
      title: 'Pending Properties',
      value: pendingProperties,
      icon: Clock,
      color: 'text-yellow-600'
    },
    {
      title: 'Blocked Properties',
      value: blockedProperties,
      icon: XCircle,
      color: 'text-red-600'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`w-4 h-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default PropertyHeader;

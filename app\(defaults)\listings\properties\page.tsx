'use client';

import React from 'react';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import { PropertiesManagement } from '@/components/pages/properties';

const PropertiesPage = () => {
    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Property Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Property Management' }]}
            />
            <div className="px-4">
                <PropertiesManagement />
            </div>
        </DefaultPageLayout>
    );
};

export default PropertiesPage;

import React, { useState } from 'react';
import { MoreHorizontal, Eye, Edit, Ban, Trash2, Star, CheckCircle, MessageSquare } from 'lucide-react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import PropertyStatusBadge from './PropertyStatusBadge';
import PropertyTypeBadge from './PropertyTypeBadge';
import PropertyActionReasonDialog from './PropertyActionReasonDialog';
import AddNoteToPropertyDialog from './AddNoteToPropertyDialog';
import { Property } from '@/utils/types/property';

interface PropertyTableRowProps {
  property: Property;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onBlock: () => void;
  onStatusChange: (status: Property['status']) => void;
  onAddNote?: (propertyId: string, note: string) => void;
}

const PropertyTableRow: React.FC<PropertyTableRowProps> = ({
  property,
  isSelected,
  onSelect,
  onDelete,
  onBlock,
  onStatusChange,
  onAddNote
}) => {
  const [reasonDialogOpen, setReasonDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'block' | 'unpublish'>('block');

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleBlockClick = () => {
    setActionType('block');
    setReasonDialogOpen(true);
  };

  const handleUnpublishClick = () => {
    setActionType('unpublish');
    setReasonDialogOpen(true);
  };

  const handleReasonConfirm = (reason: string) => {
    if (actionType === 'block') {
      onStatusChange('blocked');
    } else {
      onStatusChange('unpublished');
    }
  };

  return (
    <>
      <TableRow>
        <TableCell>
          <Checkbox checked={isSelected} onCheckedChange={onSelect} />
        </TableCell>
        <TableCell>
          <div className="flex items-center gap-3">
            <img
              src={property.images[0]}
              alt={property.title}
              className="w-12 h-12 rounded-lg object-cover"
            />
            <div>
              <div className="font-medium flex items-center gap-2">
                {property.title}
                {property.featured && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                {property.verified && <CheckCircle className="w-4 h-4 text-green-500" />}
              </div>
              <div className="text-sm text-gray-500">ID: {property.id}</div>
            </div>
          </div>
        </TableCell>
        <TableCell>
          <PropertyTypeBadge type={property.type} />
        </TableCell>
        <TableCell className="font-semibold">
          {formatPrice(property.price)}
        </TableCell>
        <TableCell>{property.location}</TableCell>
        <TableCell>
          <div>
            <div className="font-medium">{property.agentName}</div>
            <div className="text-sm text-gray-500">{property.agencyName}</div>
          </div>
        </TableCell>
        <TableCell>
          <PropertyStatusBadge 
            status={property.status} 
            onStatusChange={onStatusChange}
          />
        </TableCell>
        <TableCell>
          <div className="text-sm">
            {formatDate(property.expiryDate)}
          </div>
        </TableCell>
        <TableCell>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-white border shadow-lg z-50">
              <DropdownMenuItem>
                <Eye className="w-4 h-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="w-4 h-4 mr-2" />
                Edit Property
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleUnpublishClick}>
                <Ban className="w-4 h-4 mr-2" />
                Unpublish Property
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleBlockClick}>
                <Ban className="w-4 h-4 mr-2" />
                Block Property
              </DropdownMenuItem>
              {onAddNote && (
                <AddNoteToPropertyDialog property={property} onAddNote={onAddNote}>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Add Note
                  </DropdownMenuItem>
                </AddNoteToPropertyDialog>
              )}
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Property
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Property</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete "{property.title}"? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={onDelete}>
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      </TableRow>

      <PropertyActionReasonDialog
        isOpen={reasonDialogOpen}
        onClose={() => setReasonDialogOpen(false)}
        onConfirm={handleReasonConfirm}
        property={property}
        actionType={actionType}
      />
    </>
  );
};

export default PropertyTableRow;

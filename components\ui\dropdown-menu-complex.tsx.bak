import * as React from "react"
import { createPortal } from "react-dom"

interface DropdownMenuContextType {
  open: boolean
  onOpenChange: (open: boolean) => void
  triggerRef: React.RefObject<HTMLElement | null>
}

const DropdownMenuContext = React.createContext<DropdownMenuContextType | undefined>(undefined)

const useDropdownMenu = () => {
  const context = React.useContext(DropdownMenuContext)
  if (!context) {
    throw new Error("DropdownMenu components must be used within a DropdownMenu")
  }
  return context
}

interface DropdownMenuProps {
  children: React.ReactNode
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({ children }) => {
  const [open, setOpen] = React.useState(false)
  const triggerRef = React.useRef<HTMLElement>(null)

  return (
    <DropdownMenuContext.Provider value={{ open, onOpenChange: setOpen, triggerRef }}>
      <div className="relative inline-block text-left" data-dropdown-container>
        {children}
      </div>
    </DropdownMenuContext.Provider>
  )
}

const DropdownMenuTrigger: React.FC<{
  children: React.ReactNode
  asChild?: boolean
}> = ({ children, asChild = false }) => {
  const { open, onOpenChange, triggerRef } = useDropdownMenu()

  const handleClick = () => onOpenChange(!open)

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<any>, {
      ref: (el: HTMLElement) => {
        if (triggerRef.current !== el) {
          (triggerRef as React.MutableRefObject<HTMLElement | null>).current = el
        }
      },
      onClick: handleClick
    })
  }

  return (
    <button ref={triggerRef as React.RefObject<HTMLButtonElement>} onClick={handleClick}>
      {children}
    </button>
  )
}

interface DropdownMenuContentProps {
  children: React.ReactNode
  align?: 'start' | 'center' | 'end'
  className?: string
}

const DropdownMenuContent: React.FC<DropdownMenuContentProps> = ({
  children,
  align = 'start',
  className = ""
}) => {
  const { open, onOpenChange, triggerRef } = useDropdownMenu()
  const contentRef = React.useRef<HTMLDivElement>(null)
  const [position, setPosition] = React.useState({ top: 0, left: 0, width: 0, showAbove: false })

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contentRef.current && !contentRef.current.contains(event.target as Node)) {
        onOpenChange(false)
      }
    }

    const updatePosition = () => {
      if (triggerRef.current && open) {
        const rect = triggerRef.current.getBoundingClientRect()
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
        
        // Calculate viewport dimensions
        const viewportHeight = window.innerHeight
        const viewportWidth = window.innerWidth
        
        // Try to get actual dropdown height, fallback to estimate
        let dropdownHeight = 200
        if (contentRef.current) {
          dropdownHeight = contentRef.current.offsetHeight || 200
        }
        
        const spaceBelow = viewportHeight - rect.bottom
        const spaceAbove = rect.top
        
        // Determine if dropdown should appear above or below
        const showAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow
        
        let left = rect.left + scrollLeft
        let top: number
        
        if (showAbove) {
          // Position above the trigger
          top = rect.top + scrollTop - 4
        } else {
          // Position below the trigger (default)
          top = rect.bottom + scrollTop + 4
        }
        
        const width = rect.width
        const dropdownWidth = Math.max(width, 128)

        // Adjust horizontal position based on alignment and screen size
        if (align === 'center') {
          left = left + width / 2 - dropdownWidth / 2
        } else if (align === 'end') {
          left = left + width - dropdownWidth
        }

        // Ensure dropdown doesn't go off-screen horizontally
        const rightEdge = left + dropdownWidth
        const margin = 10
        
        if (rightEdge > viewportWidth) {
          left = viewportWidth - dropdownWidth - margin
        }
        
        if (left < margin) {
          left = margin
        }

        // For mobile screens, ensure dropdown is always visible
        if (viewportWidth < 768) { // Mobile breakpoint
          // Center dropdown on mobile if it's too wide
          if (dropdownWidth > viewportWidth - 20) {
            left = 10
          }
          // Ensure dropdown doesn't go below viewport on mobile
          if (!showAbove && top + dropdownHeight > viewportHeight + scrollTop) {
            top = rect.top + scrollTop - 4
          }
        }

        // Fallback: if still not visible, position in center of screen
        if (top < scrollTop || top > scrollTop + viewportHeight) {
          top = scrollTop + viewportHeight / 2 - dropdownHeight / 2
        }

        // Debug: Log position for troubleshooting
        console.log('Dropdown position:', { top, left, viewportWidth, viewportHeight, showAbove })

        setPosition({ top, left, width, showAbove })
      }
    }

    if (open) {
      // Wait for next frame to ensure DOM is updated
      requestAnimationFrame(() => {
        updatePosition()
        // Update again after render to get accurate dropdown height
        setTimeout(updatePosition, 0)
      })
      document.addEventListener('mousedown', handleClickOutside)
      window.addEventListener('scroll', updatePosition, true)
      window.addEventListener('resize', updatePosition)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      window.removeEventListener('scroll', updatePosition, true)
      window.removeEventListener('resize', updatePosition)
    }
  }, [open, onOpenChange, align, triggerRef])

  if (!open) return null

  const alignmentClasses = {
    start: '',
    center: 'transform -translate-x-1/2',
    end: 'transform -translate-x-full'
  }

  const verticalTransform = position.showAbove ? 'transform -translate-y-full' : ''
  const combinedTransform = `${alignmentClasses[align]} ${verticalTransform}`.trim()

  const dropdownContent = (
    <div
      ref={contentRef}
      className={`
        fixed min-w-[8rem] max-w-[90vw] min-h-[2rem] overflow-hidden rounded-md border
        bg-white p-1 shadow-lg z-[9999] opacity-100 visible ${combinedTransform} ${className}
      `}
      style={{
        top: position.top,
        left: position.left,
        minWidth: typeof window !== 'undefined' 
          ? Math.min(Math.max(position.width, 128), window.innerWidth - 20)
          : Math.max(position.width, 128),
        maxWidth: typeof window !== 'undefined' ? window.innerWidth - 20 : '90vw'
      }}
    >
      {children}
    </div>
  )

  // Use portal to render dropdown outside of table constraints
  if (typeof window !== 'undefined') {
    return createPortal(dropdownContent, document.body)
  }

  return dropdownContent
}

interface DropdownMenuItemProps {
  children: React.ReactNode
  onClick?: () => void
  onSelect?: (event: Event) => void
  className?: string
}

const DropdownMenuItem: React.FC<DropdownMenuItemProps> = ({
  children,
  onClick,
  onSelect,
  className = ""
}) => {
  const { onOpenChange } = useDropdownMenu()

  const handleClick = () => {
    onClick?.()
    onOpenChange(false)
  }

  const handleSelect = (event: React.MouseEvent) => {
    if (onSelect) {
      onSelect(event.nativeEvent)
    } else {
      handleClick()
    }
  }

  return (
    <div
      className={`
        relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm
        outline-none transition-colors hover:bg-gray-100 focus:bg-gray-100
        data-[disabled]:pointer-events-none data-[disabled]:opacity-50
        ${className}
      `}
      onClick={handleSelect}
    >
      {children}
    </div>
  )
}

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
}

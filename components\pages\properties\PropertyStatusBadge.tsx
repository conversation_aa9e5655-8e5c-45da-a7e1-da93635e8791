import React from 'react';
import { CheckCircle, XCircle, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Property } from '@/utils/types/property';

interface PropertyStatusBadgeProps {
  status: Property['status'];
  onStatusChange?: (newStatus: Property['status']) => void;
}

const PropertyStatusBadge: React.FC<PropertyStatusBadgeProps> = ({ status, onStatusChange }) => {
  const variants = {
    available: { variant: 'default' as const, icon: CheckCircle },
    sold: { variant: 'default' as const, icon: CheckCircle },
    rented: { variant: 'default' as const, icon: CheckCircle },
    unpublished: { variant: 'secondary' as const, icon: Eye },
    blocked: { variant: 'destructive' as const, icon: XCircle }
  };

  const statusOptions: Property['status'][] = ['available', 'sold', 'rented', 'unpublished', 'blocked'];

  const config = variants[status];
  const Icon = config.icon;

  if (!onStatusChange) {
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="cursor-pointer">
          <Badge variant={config.variant} className="flex items-center gap-1 hover:opacity-80 transition-opacity">
            <Icon className="w-3 h-3" />
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white border shadow-lg z-50">
        {statusOptions.map((statusOption) => (
          <DropdownMenuItem
            key={statusOption}
            onClick={() => onStatusChange(statusOption)}
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-100"
          >
            {React.createElement(variants[statusOption].icon, { className: "w-4 h-4" })}
            {statusOption.charAt(0).toUpperCase() + statusOption.slice(1)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PropertyStatusBadge;

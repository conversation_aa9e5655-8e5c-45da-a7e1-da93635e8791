export interface Property {
  id: string;
  title: string;
  type: 'residential' | 'commercial' | 'land' | 'rental';
  location: string;
  price: number;
  status: 'available' | 'sold' | 'rented' | 'unpublished' | 'blocked';
  agentName: string;
  agencyName: string;
  expiryDate: string;
  images: string[];
  featured?: boolean;
  verified?: boolean;
  description?: string;
  bedrooms?: number;
  bathrooms?: number;
  area?: string;
  yearBuilt?: number;
  features?: string[];
  listingType?: 'rent' | 'sale';
  emirate?: string;
  createdAt?: string;
  updatedAt?: string;
  views?: number;
  notes?: PropertyNote[];
}

export interface PropertyNote {
  id: string;
  propertyId: string;
  note: string;
  createdBy: string;
  createdAt: string;
}

export interface PropertyFilter {
  search: string;
  status: string;
  type: string;
  location: string;
  listingType: string;
  priceRange?: {
    min: number;
    max: number;
  };
}

export interface PropertyStats {
  totalProperties: number;
  activeProperties: number;
  pendingProperties: number;
  blockedProperties: number;
}

export type PropertySortField = 'title' | 'price' | 'location' | 'expiryDate' | 'createdAt';
export type SortOrder = 'asc' | 'desc';

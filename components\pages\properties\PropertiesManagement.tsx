import React, { useState, useEffect } from 'react';
import { Property, PropertyStats } from '@/utils/types/property';
import PropertyHeader from './PropertyHeader';
import PropertyFilters from './PropertyFilters';
import PropertyTable from './PropertyTable';

const PropertiesManagement: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [locationFilter, setLocationFilter] = useState('all');
  const [listingTypeFilter, setListingTypeFilter] = useState('all');

  // Sorting states
  const [sortBy, setSortBy] = useState('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Mock data for properties
  const mockProperties: Property[] = [
    {
      id: '1',
      title: 'Modern 3BR Apartment in Downtown',
      type: 'residential',
      location: 'Downtown, Dubai',
      price: 1200000,
      status: 'available',
      agentName: 'John Smith',
      agencyName: 'Elite Properties',
      expiryDate: '2024-12-31',
      images: ['/default.png'],
      featured: true,
      verified: true,
      bedrooms: 3,
      bathrooms: 2,
      area: '1200 sq ft',
      listingType: 'sale',
      emirate: 'dubai'
    },
    {
      id: '2',
      title: 'Luxury Villa with Pool',
      type: 'residential',
      location: 'Palm Jumeirah, Dubai',
      price: 2500000,
      status: 'sold',
      agentName: 'Sarah Johnson',
      agencyName: 'Premium Homes',
      expiryDate: '2024-11-30',
      images: ['/default.png'],
      featured: false,
      verified: true,
      bedrooms: 5,
      bathrooms: 4,
      area: '3500 sq ft',
      listingType: 'sale',
      emirate: 'dubai'
    },
    {
      id: '3',
      title: 'Commercial Office Space',
      type: 'commercial',
      location: 'Business Bay, Dubai',
      price: 800000,
      status: 'available',
      agentName: 'Mike Wilson',
      agencyName: 'Business Properties',
      expiryDate: '2025-01-15',
      images: ['/default.png'],
      featured: false,
      verified: false,
      area: '2000 sq ft',
      listingType: 'rent',
      emirate: 'dubai'
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setProperties(mockProperties);
    }, 1000);
  }, []);

  // Filter properties
  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.agentName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === 'all' || property.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || property.status === statusFilter;
    const matchesLocation = locationFilter === 'all' || property.emirate === locationFilter;
    const matchesListingType = listingTypeFilter === 'all' || property.listingType === listingTypeFilter;

    return matchesSearch && matchesType && matchesStatus && matchesLocation && matchesListingType;
  });

  // Sort properties
  const sortedProperties = [...filteredProperties].sort((a, b) => {
    let aValue: any = a[sortBy as keyof Property];
    let bValue: any = b[sortBy as keyof Property];

    if (sortBy === 'price') {
      aValue = Number(aValue);
      bValue = Number(bValue);
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Calculate stats
  const stats: PropertyStats = {
    totalProperties: properties.length,
    activeProperties: properties.filter(p => p.status === 'available').length,
    pendingProperties: properties.filter(p => p.status === 'unpublished').length,
    blockedProperties: properties.filter(p => p.status === 'blocked').length
  };

  // Check if filters are applied
  const hasFilters = searchTerm !== '' || typeFilter !== 'all' || statusFilter !== 'all' ||
                    locationFilter !== 'all' || listingTypeFilter !== 'all';

  // Event handlers
  const handleSelectProperty = (propertyId: string) => {
    setSelectedProperties(prev =>
      prev.includes(propertyId)
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedProperties(checked ? sortedProperties.map(p => p.id) : []);
  };

  const handleDeleteProperty = (propertyId: string) => {
    setProperties(prev => prev.filter(p => p.id !== propertyId));
    setSelectedProperties(prev => prev.filter(id => id !== propertyId));
  };

  const handleBlockProperty = (propertyId: string) => {
    setProperties(prev => prev.map(p =>
      p.id === propertyId ? { ...p, status: 'blocked' as const } : p
    ));
  };

  const handleStatusChange = (propertyId: string, status: Property['status']) => {
    setProperties(prev => prev.map(p =>
      p.id === propertyId ? { ...p, status } : p
    ));
  };

  const handleAddNote = (propertyId: string, note: string) => {
    console.log('Adding note to property:', propertyId, note);
    // In a real implementation, this would save the note to the backend
  };

  const handleBulkDelete = () => {
    setProperties(prev => prev.filter(p => !selectedProperties.includes(p.id)));
    setSelectedProperties([]);
  };

  const handleBulkBlock = () => {
    setProperties(prev => prev.map(p =>
      selectedProperties.includes(p.id) ? { ...p, status: 'blocked' as const } : p
    ));
    setSelectedProperties([]);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setTypeFilter('all');
    setStatusFilter('all');
    setLocationFilter('all');
    setListingTypeFilter('all');
  };

  return (
    <div className="space-y-6">
      <PropertyHeader
        totalProperties={stats.totalProperties}
        activeProperties={stats.activeProperties}
        pendingProperties={stats.pendingProperties}
        blockedProperties={stats.blockedProperties}
      />

      <PropertyFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        typeFilter={typeFilter}
        setTypeFilter={setTypeFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        locationFilter={locationFilter}
        setLocationFilter={setLocationFilter}
        listingTypeFilter={listingTypeFilter}
        setListingTypeFilter={setListingTypeFilter}
        filteredCount={filteredProperties.length}
        totalCount={properties.length}
        hasFilters={hasFilters}
        onClearFilters={handleClearFilters}
      />

      <PropertyTable
        properties={sortedProperties}
        selectedProperties={selectedProperties}
        onSelectProperty={handleSelectProperty}
        onSelectAll={handleSelectAll}
        onDeleteProperty={handleDeleteProperty}
        onBlockProperty={handleBlockProperty}
        onStatusChange={handleStatusChange}
        onAddNote={handleAddNote}
        onBulkDelete={handleBulkDelete}
        onBulkBlock={handleBulkBlock}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSort={handleSort}
      />
    </div>
  );
};

export default PropertiesManagement;
